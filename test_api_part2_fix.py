import requests
import json

# Test smart exam generation với format Part II mới
url = "http://127.0.0.1:8000/api/exam-generation/generate-smart-exam"

# Request data
data = {
    "school_name": "Trường THPT Hong Thinh",
    "subject": "<PERSON><PERSON><PERSON> học",
    "grade": "Grade 12",
    "exam_matrix": [
        {
            "lesson_id": "test1",
            "yeu_cau_can_dat": "Hiểu về nguyên tố hóa học",
            "muc_do": {
                "Biết": 2,
                "Hiểu": 2,
                "Vận_dụng": 1
            }
        }
    ]
}

print("=== TESTING SMART EXAM API WITH PART II FIX ===")
print(f"URL: {url}")
print(f"Data: {json.dumps(data, indent=2, ensure_ascii=True)}")

try:
    response = requests.post(url, json=data)
    print(f"\nStatus Code: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"Success: {result.get('success')}")
        print(f"Document URL: {result.get('document_url')}")
        
        # In thống kê
        stats = result.get('statistics', {})
        print(f"\nStatistics:")
        print(f"- Total questions: {stats.get('total_questions')}")
        print(f"- Part 1: {stats.get('part_1_questions')}")
        print(f"- Part 2: {stats.get('part_2_questions')}")
        print(f"- Part 3: {stats.get('part_3_questions')}")
        print(f"- Difficulty: {stats.get('difficulty_distribution')}")
        
    else:
        print(f"Error: {response.text}")
        
except Exception as e:
    print(f"Exception: {e}")
