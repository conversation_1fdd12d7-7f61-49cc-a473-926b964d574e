#!/usr/bin/env python3

import asyncio
import sys
import os

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from motor.motor_asyncio import AsyncIOMotorClient
from app.core.config import settings
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def check_mongodb():
    """Kiểm tra MongoDB và liệt kê collections"""
    
    try:
        logger.info("=== CHECKING MONGODB ===")
        logger.info(f"MongoDB URL: {settings.MONGODB_URL}")
        logger.info(f"Database: {settings.MONGODB_DATABASE}")
        
        # Connect to MongoDB
        client = AsyncIOMotorClient(settings.MONGODB_URL)
        db = client[settings.MONGODB_DATABASE]
        
        # List collections
        collections = await db.list_collection_names()
        logger.info(f"Collections: {collections}")
        
        # Check each collection for sample data
        for collection_name in collections:
            collection = db[collection_name]
            count = await collection.count_documents({})
            logger.info(f"\nCollection '{collection_name}': {count} documents")
            
            if count > 0:
                # Get sample documents
                sample_docs = await collection.find({}).limit(3).to_list(length=3)
                for i, doc in enumerate(sample_docs):
                    logger.info(f"  Sample {i+1}: {list(doc.keys())}")
                    
                    # Look for lesson-related fields
                    for key in ["lesson_id", "_id", "id", "title", "name"]:
                        if key in doc:
                            logger.info(f"    {key}: {doc[key]}")
                            
        # Close connection
        client.close()
        
    except Exception as e:
        logger.error(f"Error checking MongoDB: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(check_mongodb())
