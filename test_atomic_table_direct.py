#!/usr/bin/env python3
"""
Test trực tiếp hàm tạo bảng nguyên tử khối
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.smart_exam_docx_service import SmartExamDocxService
from docx import Document

def test_atomic_table_creation():
    """Test trực tiếp việc tạo bảng nguyên tử khối"""
    
    # Tạo service
    service = SmartExamDocxService()
    
    # Tạo document test
    doc = Document()
    
    # Tạo dữ liệu câu hỏi mẫu với nội dung hóa học
    sample_questions = [
        {
            "question": "Nguyên tử của nguyên tố nào sau đây có 6 electron ở lớp ngoài cùng?",
            "answer": {
                "A": "C (Z=6)",
                "B": "O (Z=8)", 
                "C": "S (Z=16)",
                "D": "Ne (Z=10)"
            },
            "explanation": "Cấu hình electron của S là [Ne]3s²3p⁴, có 6 electron lớp ngoài cùng"
        },
        {
            "question": "Phản ứng nào sau đây tạo ra H2O?",
            "answer": {
                "A": "H2 + O2 → H2O",
                "B": "NaOH + HCl → NaCl + H2O",
                "C": "CaO + H2O → Ca(OH)2",
                "D": "Tất cả đều đúng"
            },
            "explanation": "Các phản ứng đều có sản phẩm H2O với các nguyên tố H, O, Na, Cl, Ca"
        },
        {
            "question": "Hợp chất nào có công thức Fe2O3?",
            "answer": {
                "A": "Sắt (II) oxit",
                "B": "Sắt (III) oxit", 
                "C": "Sắt (IV) oxit",
                "D": "Không có đáp án đúng"
            },
            "explanation": "Fe2O3 là sắt (III) oxit, chứa nguyên tố Fe và O"
        }
    ]
    
    print("Testing atomic table creation...")
    print(f"Sample questions: {len(sample_questions)}")
    
    # Test hàm trích xuất nguyên tố
    print("\n=== Testing element extraction ===")
    elements = service._extract_chemical_elements_from_questions(sample_questions)
    print(f"Extracted elements: {elements}")
    
    # Test hàm tạo chuỗi nguyên tử khối
    print("\n=== Testing atomic mass string creation ===")
    if elements:
        atomic_masses_text = service._get_atomic_masses_for_elements(elements)
        print(f"Atomic masses text: {atomic_masses_text}")
    else:
        print("No elements found, using default elements")
        default_elements = ["H", "C", "N", "O", "Na", "Mg", "Al", "Si", "P", "S", "Cl", "K", "Ca", "Fe", "Cu", "Zn"]
        atomic_masses_text = service._get_atomic_masses_for_elements(default_elements)
        print(f"Default atomic masses text: {atomic_masses_text}")
    
    # Test tạo bảng trong document
    print("\n=== Testing document creation ===")
    try:
        service._create_chemistry_valence_table(doc, sample_questions)
        print("Chemistry table created successfully!")
        
        # Lưu file test
        test_file = "test_atomic_table.docx"
        doc.save(test_file)
        print(f"Test document saved as: {test_file}")
        
        # Kiểm tra nội dung document
        print(f"Document has {len(doc.paragraphs)} paragraphs")
        for i, para in enumerate(doc.paragraphs):
            if para.text.strip():
                print(f"Paragraph {i+1}: {para.text[:100]}...")
                
    except Exception as e:
        print(f"Error creating chemistry table: {e}")
        import traceback
        traceback.print_exc()

def test_subject_detection():
    """Test việc phát hiện môn hóa học"""
    
    service = SmartExamDocxService()
    
    test_subjects = [
        "Hoa hoc",
        "Chemistry",
        "Toan hoc",
        "Vat ly"
    ]
    
    print("\n=== Testing subject detection ===")
    for subject in test_subjects:
        is_chemistry = "hóa" in subject.lower() or "hoa" in subject.lower()
        print(f"Subject: '{subject}' -> Is chemistry: {is_chemistry}")

if __name__ == "__main__":
    test_subject_detection()
    test_atomic_table_creation()
