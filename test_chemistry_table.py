#!/usr/bin/env python3
"""
Test script để kiểm tra bảng nguyên tử khối trong đề thi hóa học
"""

import requests
import json

def test_chemistry_exam():
    """Test tạo đề thi hóa học để kiểm tra bảng nguyên tử khối"""
    
    url = "http://localhost:8000/api/v1/exam/generate-smart-exam"
    
    payload = {
        "school": "Trường THPT ABC",
        "grade": 12,
        "subject": "Hoa hoc",  # Chemistry subject
        "examTitle": "Kiểm tra giữa kỳ 1",
        "duration": 45,
        "outputFormat": "docx",
        "outputLink": "online",
        "matrix": [
            {
                "lessonId": "hoa12_bai1_nguyen_tu_nguyen_to_bang_tuan_hoan",
                "totalQuestions": 6,
                "parts": [
                    {
                        "part": 1,
                        "objectives": {
                            "Biết": 2,
                            "Hiểu": 0,
                            "Vận_dụng": 0
                        }
                    },
                    {
                        "part": 2,
                        "objectives": {
                            "Biết": 0,
                            "Hiểu": 2,
                            "Vận_dụng": 0
                        }
                    },
                    {
                        "part": 3,
                        "objectives": {
                            "Biết": 0,
                            "Hiểu": 1,
                            "Vận_dụng": 1
                        }
                    }
                ]
            }
        ]
    }
    
    print("Sending request to generate chemistry exam...")
    print(f"Subject: {payload['subject']}")
    print(f"Lesson: {payload['matrix'][0]['lessonId']}")

    try:
        response = requests.post(url, json=payload, timeout=120)

        if response.status_code == 200:
            result = response.json()
            print(f"Success: {result.get('success', False)}")
            print(f"Message: {result.get('message', '')}")

            if result.get('success'):
                online_links = result.get('online_links', {})
                if online_links:
                    print("Online links:")
                    for key, link in online_links.items():
                        print(f"   {key}: {link}")
                else:
                    print("No online links found")

                statistics = result.get('statistics', {})
                if statistics:
                    print(f"Statistics: {statistics}")
            else:
                print(f"Error: {result.get('error', 'Unknown error')}")
        else:
            print(f"HTTP Error {response.status_code}: {response.text}")

    except requests.exceptions.Timeout:
        print("Request timeout - exam generation took too long")
    except requests.exceptions.RequestException as e:
        print(f"Request error: {e}")
    except Exception as e:
        print(f"Unexpected error: {e}")

if __name__ == "__main__":
    test_chemistry_exam()
