"""
Test script để kiểm tra định dạng hóa học và đáp án số trong đề thi
"""

import requests
import json

# Test data với các vấn đề định dạng hóa học
test_request = {
    "subject": "<PERSON><PERSON><PERSON> học",
    "grade": "12",
    "duration": 50,
    "school": "THPT ABC",
    "parts": [
        {
            "part": 1,
            "objectives": {
                "Biết": 2,
                "Hiểu": 1,
                "Vận_dụng": 0
            }
        },
        {
            "part": 2,
            "objectives": {
                "Biết": 1,
                "Hiểu": 1,
                "Vận_dụng": 0
            }
        },
        {
            "part": 3,
            "objectives": {
                "Biết": 0,
                "Hiể<PERSON>": 1,
                "Vận_dụng": 1
            }
        }
    ],
    "lessons": [
        {
            "lesson_id": "hoa12_c1_b1",
            "yeu_cau_can_dat": "Cấu tạo nguyên tử, đồng vị",
            "muc_do": "Biết"
        }
    ]
}

def test_smart_exam_generation():
    """Test tạo đề thi thông minh"""
    url = "http://localhost:8000/api/smart-exam/generate"

    print("Testing Smart Exam Generation...")
    print(f"Request URL: {url}")
    print("Request data prepared...")

    try:
        response = requests.post(url, json=test_request, timeout=120)
        print(f"Response status: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print("Smart exam generated successfully!")

            # Kiểm tra có file DOCX không
            if result.get("docx_info") and result["docx_info"].get("success"):
                print(f"DOCX file created: {result['docx_info']['filename']}")
                print(f"File path: {result['docx_info']['file_path']}")
                print(f"File size: {result['docx_info']['file_size']} bytes")

                # Kiểm tra questions để xem có vấn đề định dạng không
                questions = result.get("questions", [])
                print(f"\nChecking {len(questions)} questions for formatting issues:")

                for i, q in enumerate(questions[:3]):  # Chỉ check 3 câu đầu
                    print(f"\nCau {i+1} (Part {q.get('part', 'N/A')}):")
                    question_text = q.get("question", "")

                    # Kiểm tra có thẻ HTML không
                    if "<sup>" in question_text or "<sub>" in question_text:
                        print(f"  WARNING: Found HTML tags: {question_text[:100]}...")
                    else:
                        print(f"  OK: Clean format: {question_text[:100]}...")

                    # Kiểm tra đáp án phần 3
                    if q.get("part") == 3:
                        answer = q.get("answer", {})
                        raw_answer = answer.get("dap_an", answer.get("answer", ""))
                        print(f"  Part 3 answer: '{raw_answer}'")

                        # Kiểm tra có đơn vị không
                        if any(unit in str(raw_answer) for unit in ["mol", "g", "%", "M", "L", "ml"]):
                            print(f"  WARNING: Answer contains units: {raw_answer}")
                        else:
                            print(f"  OK: Clean numeric answer: {raw_answer}")

                return True
            else:
                print("DOCX creation failed")
                print(f"Error: {result.get('docx_info', {}).get('error', 'Unknown error')}")
                return False
        else:
            print(f"Request failed with status {response.status_code}")
            print(f"Error: {response.text}")
            return False

    except requests.exceptions.Timeout:
        print("Request timed out")
        return False
    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == "__main__":
    print("Testing Chemistry Format Fixes")
    print("=" * 50)

    success = test_smart_exam_generation()

    if success:
        print("\nTest completed successfully!")
        print("Check the generated DOCX file for:")
        print("  1. Proper chemistry notation (Li-6 instead of <sup>6</sup>Li)")
        print("  2. Subscripts (S8 instead of S<sub>8</sub>)")
        print("  3. Numeric-only answers in Part III")
        print("  4. Vietnamese 'Dung/Sai' in Part II answers")
    else:
        print("\nTest failed!")
