"""
Test cuối cùng để kiểm tra định dạng hóa học và đáp án số
"""

import requests
import json

# Test data theo format SmartExamRequest
test_request = {
    "school": "THPT ABC",
    "grade": 12,
    "subject": "<PERSON><PERSON><PERSON> học",
    "examTitle": "Kiểm tra định dạng hóa học",
    "duration": 50,
    "outputFormat": "docx",
    "outputLink": "online",
    "matrix": [
        {
            "lessonId": "hoa12_c1_b1",
            "totalQuestions": 3,
            "parts": [
                {
                    "part": 1,
                    "objectives": {
                        "Biết": 1,
                        "Hiểu": 0,
                        "Vận_dụng": 0
                    }
                },
                {
                    "part": 2,
                    "objectives": {
                        "Biết": 1,
                        "Hiểu": 0,
                        "Vận_dụng": 0
                    }
                },
                {
                    "part": 3,
                    "objectives": {
                        "Biết": 0,
                        "Hiểu": 1,
                        "Vận_dụng": 0
                    }
                }
            ]
        }
    ]
}

def test_final_exam():
    """Test tạo đề thi với định dạng đã sửa"""
    url = "http://localhost:8000/api/v1/exam/generate-smart-exam"
    
    print("Testing Final Exam Format...")
    print("=" * 50)
    
    try:
        response = requests.post(url, json=test_request, timeout=120)
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("SUCCESS: Exam generated!")
            print(f"Response keys: {list(result.keys())}")

            # In toàn bộ response để debug
            print(f"Full response: {json.dumps(result, indent=2, ensure_ascii=False)}")

            # Kiểm tra DOCX
            if result.get("docx_info") and result["docx_info"].get("success"):
                print(f"DOCX created: {result['docx_info']['filename']}")
                print(f"File path: {result['docx_info']['file_path']}")

                # Kiểm tra questions
                questions = result.get("questions", [])
                print(f"\nGenerated {len(questions)} questions:")

                for i, q in enumerate(questions):
                    part = q.get("part", "N/A")
                    question_text = q.get("question", "")[:80] + "..."
                    print(f"  Q{i+1} (Part {part}): {question_text}")

                    # Kiểm tra đáp án phần 3
                    if part == 3:
                        answer = q.get("answer", {})
                        raw_answer = answer.get("dap_an", answer.get("answer", ""))
                        print(f"    Answer: {raw_answer}")

                print("\nSUCCESS: Check the DOCX file for:")
                print("1. Chemistry notation without HTML tags")
                print("2. Part III instructions with format note")
                print("3. Numeric answers in Vietnamese format (comma decimal)")
                print("4. Vietnamese 'Dung/Sai' in Part II")

                return True
            else:
                print("FAILED: DOCX creation failed")
                if result.get("docx_info"):
                    print(f"DOCX error: {result['docx_info']}")
                return False
        else:
            print(f"FAILED: HTTP {response.status_code}")
            print(response.text[:500])
            return False
            
    except Exception as e:
        print(f"ERROR: {e}")
        return False

if __name__ == "__main__":
    print("Final Format Test")
    print("=" * 60)
    
    success = test_final_exam()
    
    if success:
        print("\nALL TESTS PASSED!")
        print("The exam format has been fixed:")
        print("- Chemistry notation: Li-6 instead of <sup>6</sup>Li")
        print("- Part III format note added")
        print("- Numeric answers: Vietnamese decimal format")
    else:
        print("\nTEST FAILED!")
