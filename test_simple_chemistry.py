#!/usr/bin/env python3
"""
Test script đơn giản để kiểm tra API
"""

import requests
import json

def test_simple_request():
    """Test đơn giản"""
    
    # Kiểm tra xem có lesson nào không
    url = "http://localhost:8000/api/v1/pdf/search-lessons"
    
    payload = {
        "query": "nguyen tu",
        "limit": 5
    }
    
    print("Testing lesson search...")
    
    try:
        response = requests.post(url, json=payload, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print(f"Success: {result.get('success', False)}")
            
            lessons = result.get('lessons', [])
            print(f"Found {len(lessons)} lessons")
            
            for i, lesson in enumerate(lessons[:3]):
                print(f"Lesson {i+1}: {lesson.get('lesson_id', 'No ID')}")
                print(f"  Title: {lesson.get('title', 'No title')}")
                print(f"  Content preview: {lesson.get('content', '')[:100]}...")
                print()
                
        else:
            print(f"HTTP Error {response.status_code}: {response.text}")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_simple_request()
