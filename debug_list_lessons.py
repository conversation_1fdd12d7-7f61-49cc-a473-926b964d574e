#!/usr/bin/env python3

import asyncio
import sys
import os

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.qdrant_service import QdrantService
from app.database.mongodb import get_database
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def list_available_lessons():
    """Liệt kê các lesson ID có sẵn trong database"""
    
    try:
        logger.info("=== LISTING AVAILABLE LESSONS ===")
        
        # Check MongoDB first
        logger.info("\n1. Checking MongoDB...")
        db = await get_database()
        lessons_collection = db.lessons
        
        # Get some sample lessons
        lessons_cursor = lessons_collection.find({}).limit(10)
        lessons = await lessons_cursor.to_list(length=10)
        
        logger.info(f"Found {len(lessons)} lessons in MongoDB:")
        for lesson in lessons:
            lesson_id = lesson.get("_id") or lesson.get("lesson_id") or lesson.get("id")
            title = lesson.get("title", "No title")
            logger.info(f"  - ID: {lesson_id}, Title: {title}")
        
        # Check Qdrant
        logger.info("\n2. Checking Qdrant...")
        qdrant_service = QdrantService()
        
        # Get collections
        collections = await qdrant_service.client.get_collections()
        logger.info(f"Qdrant collections: {[c.name for c in collections.collections]}")
        
        # Try to get some points from each collection
        for collection in collections.collections:
            collection_name = collection.name
            logger.info(f"\nChecking collection: {collection_name}")
            
            try:
                # Get some sample points
                points = await qdrant_service.client.scroll(
                    collection_name=collection_name,
                    limit=5,
                    with_payload=True
                )
                
                logger.info(f"Found {len(points[0])} points in {collection_name}")
                
                for point in points[0]:
                    payload = point.payload
                    lesson_id = payload.get("lesson_id") or payload.get("id") or payload.get("_id")
                    title = payload.get("title", "No title")
                    logger.info(f"  - Point ID: {point.id}, Lesson ID: {lesson_id}, Title: {title}")
                    
            except Exception as e:
                logger.warning(f"Error reading from collection {collection_name}: {e}")
                
    except Exception as e:
        logger.error(f"Error listing lessons: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(list_available_lessons())
