#!/usr/bin/env python3
"""
Simple test for smart exam endpoint
"""

import requests
import json

def test_endpoint():
    url = "http://localhost:8001/api/v1/exam/generate-smart-exam"
    
    payload = {
        "school": "Truong THPT Hong Thinh",
        "grade": 12,
        "subject": "Hoa hoc",
        "examTitle": "Kiem tra con cat",
        "duration": 90,
        "outputFormat": "docx",
        "outputLink": "online",
        "matrix": [
            {
                "lessonId": "234",
                "totalQuestions": 10,
                "parts": [
                    {
                        "part": 1,
                        "objectives": {
                            "Biết": 3,
                            "Hiểu": 1,
                            "Vận_dụng": 0
                        }
                    },
                    {
                        "part": 2,
                        "objectives": {
                            "Biết": 1,
                            "Hiểu": 3,
                            "Vận_dụng": 0
                        }
                    },
                    {
                        "part": 3,
                        "objectives": {
                            "Biết": 0,
                            "Hiểu": 1,
                            "Vận_dụng": 1
                        }
                    }
                ]
            }
        ]
    }
    
    try:
        print("Sending request to smart exam endpoint...")
        response = requests.post(url, json=payload, timeout=120)
        
        print(f"Status code: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print("SUCCESS: Got JSON response")
                print(f"Response keys: {list(result.keys())}")
                
                if result.get("success"):
                    print(f"Exam generated successfully!")
                    statistics = result.get('statistics', {})
                    total_questions = statistics.get('total_questions', 0) if isinstance(statistics, dict) else 0
                    print(f"Total questions: {total_questions}")
                    print(f"Statistics: {statistics}")
                else:
                    print(f"Exam generation failed: {result.get('error', 'Unknown error')}")
                    
            except json.JSONDecodeError:
                print("Response is not JSON:")
                print(response.text[:500])
        else:
            print(f"HTTP Error: {response.status_code}")
            print(response.text[:500])
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_endpoint()
