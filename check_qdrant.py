#!/usr/bin/env python3
"""
Script để kiểm tra dữ liệu trong Qdrant
"""

import asyncio
import sys
import os

# Add the app directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.services.qdrant_service import qdrant_service
from qdrant_client import models as qdrant_models

async def check_qdrant():
    try:
        print("Checking Qdrant connection and data...")

        # Kiểm tra kết nối
        if not qdrant_service.qdrant_client:
            print('ERROR: Qdrant client not available')
            return

        print('SUCCESS: Qdrant client connected')

        # Lấy danh sách collections
        collections = qdrant_service.qdrant_client.get_collections().collections
        print(f'Found {len(collections)} collections:')
        
        for collection in collections:
            print(f'  - {collection.name}')
            
            # Đếm số points trong collection
            try:
                count_result = qdrant_service.qdrant_client.count(collection_name=collection.name)
                print(f'    Points: {count_result.count}')
                
                # <PERSON><PERSON>y một vài sample points để kiểm tra structure
                if count_result.count > 0:
                    sample = qdrant_service.qdrant_client.scroll(
                        collection_name=collection.name,
                        limit=2,
                        with_payload=True
                    )
                    
                    if sample[0]:
                        payload_keys = list(sample[0][0].payload.keys()) if sample[0][0].payload else []
                        print(f'    Sample payload keys: {payload_keys}')
                        
                        # Kiểm tra lesson_id cụ thể
                        if 'lesson_id' in (sample[0][0].payload or {}):
                            lesson_id = sample[0][0].payload['lesson_id']
                            print(f'    Sample lesson_id: {lesson_id}')
                            
            except Exception as e:
                print(f'    Error checking collection: {e}')
        
        # Test tìm kiếm lesson_id cụ thể
        test_lesson_ids = ['234', 'test1']
        print(f'\nTesting search for lesson_ids: {test_lesson_ids}')

        for lesson_id in test_lesson_ids:
            found = False
            for collection in collections:
                if collection.name.startswith('textbook_'):
                    try:
                        search_result = qdrant_service.qdrant_client.scroll(
                            collection_name=collection.name,
                            scroll_filter=qdrant_models.Filter(
                                must=[
                                    qdrant_models.FieldCondition(
                                        key='lesson_id',
                                        match=qdrant_models.MatchValue(value=lesson_id),
                                    )
                                ]
                            ),
                            limit=1,
                            with_payload=True,
                        )

                        if search_result[0]:
                            print(f'  FOUND lesson_id "{lesson_id}" in {collection.name}')
                            payload = search_result[0][0].payload
                            print(f'      Title: {payload.get("lesson_title", "N/A")}')
                            print(f'      Chapter: {payload.get("chapter_title", "N/A")}')
                            print(f'      Content preview: {payload.get("text", "")[:100]}...')
                            found = True
                            break
                    except Exception as e:
                        continue

            if not found:
                print(f'  NOT FOUND lesson_id "{lesson_id}" in any collection')
        
        # Kiểm tra MongoDB fallback
        print(f'\nChecking MongoDB fallback...')
        try:
            from motor.motor_asyncio import AsyncIOMotorClient
            from app.core.config import settings

            client = AsyncIOMotorClient(settings.MONGODB_URL)
            db = client[settings.MONGODB_DATABASE]

            # Đếm textbooks
            textbook_count = await db.textbooks.count_documents({})
            print(f'  MongoDB textbooks: {textbook_count}')

            if textbook_count > 0:
                # Lấy sample textbook
                sample_book = await db.textbooks.find_one({})
                if sample_book:
                    print(f'  Sample book ID: {sample_book.get("book_id", "N/A")}')
                    print(f'  Sample book title: {sample_book.get("title", "N/A")}')

                    # Kiểm tra lessons
                    chapters = sample_book.get("chapters", [])
                    total_lessons = 0
                    sample_lesson_ids = []

                    for chapter in chapters:
                        lessons = chapter.get("lessons", [])
                        total_lessons += len(lessons)
                        for lesson in lessons[:2]:  # Lấy 2 lesson đầu
                            sample_lesson_ids.append(lesson.get("lesson_id", ""))

                    print(f'  Total lessons in sample book: {total_lessons}')
                    print(f'  Sample lesson IDs: {sample_lesson_ids}')

        except Exception as e:
            print(f'  ERROR MongoDB check failed: {e}')

    except Exception as e:
        print(f'ERROR: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(check_qdrant())
