#!/usr/bin/env python3
"""
Direct debug script to test exam content service
"""
import asyncio
import logging
import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.abspath('.'))

from app.services.exam_content_service import exam_content_service

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_lesson_content():
    """Test lesson content retrieval directly"""
    logger.info("=== DIRECT LESSON CONTENT TEST ===")
    
    # Test with lesson ID 234
    lesson_ids = ["234"]
    
    try:
        logger.info(f"Testing lesson content retrieval for: {lesson_ids}")
        
        result = await exam_content_service.get_multiple_lessons_content_for_exam(
            lesson_ids=lesson_ids
        )
        
        logger.info(f"Result success: {result.get('success')}")
        logger.info(f"Result keys: {list(result.keys())}")
        
        if result.get('success'):
            content = result.get('content', {})
            logger.info(f"Content keys: {list(content.keys())}")
            
            for lesson_id, lesson_data in content.items():
                logger.info(f"Lesson {lesson_id}:")
                logger.info(f"  Type: {type(lesson_data)}")
                if isinstance(lesson_data, dict):
                    logger.info(f"  Keys: {list(lesson_data.keys())}")
                    
                    # Check if it has the expected structure
                    if 'content' in lesson_data:
                        main_content = lesson_data['content']
                        logger.info(f"  Main content type: {type(main_content)}")
                        logger.info(f"  Main content preview: {str(main_content)[:200]}...")
                    else:
                        logger.warning(f"  No 'content' key found!")
                        
        else:
            logger.error(f"Failed to get lesson content: {result.get('error')}")
            
    except Exception as e:
        logger.error(f"Exception during test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_lesson_content())
