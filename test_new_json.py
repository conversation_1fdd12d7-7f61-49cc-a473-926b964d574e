#!/usr/bin/env python3
"""
Test script for Smart Exam API with new JSON data
"""

import requests
import json
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_smart_exam_api():
    """Test the smart exam generation API"""
    
    # API endpoint
    url = "http://127.0.0.1:8000/api/v1/exam/generate-smart-exam"
    
    # Test data
    test_data = {
        "school": "Trường THPT Lê Hồng Phong",
        "grade": 12,
        "subject": "<PERSON>h học",
        "examTitle": "Kiểm tra cuối chương - <PERSON> t<PERSON>ền học",
        "duration": 90,
        "outputFormat": "docx",
        "outputLink": "online",
        "matrix": [
            {
                "lessonId": "234",
                "totalQuestions": 10,
                "parts": [
                    {
                        "part": 1,
                        "objectives": {
                            "Biết": 3,
                            "Hiểu": 1,
                            "Vận_dụng": 0
                        }
                    },
                    {
                        "part": 2,
                        "objectives": {
                            "Biết": 1,
                            "Hiểu": 3,
                            "Vận_dụng": 0
                        }
                    },
                    {
                        "part": 3,
                        "objectives": {
                            "Biết": 0,
                            "Hiểu": 1,
                            "Vận_dụng": 1
                        }
                    }
                ]
            },
            {
                "lessonId": "test1",
                "totalQuestions": 8,
                "parts": [
                    {
                        "part": 1,
                        "objectives": {
                            "Biết": 2,
                            "Hiểu": 1,
                            "Vận_dụng": 0
                        }
                    },
                    {
                        "part": 2,
                        "objectives": {
                            "Biết": 0,
                            "Hiểu": 3,
                            "Vận_dụng": 0
                        }
                    },
                    {
                        "part": 3,
                        "objectives": {
                            "Biết": 0,
                            "Hiểu": 1,
                            "Vận_dụng": 1
                        }
                    }
                ]
            }
        ]
    }
    
    logger.info("Starting Smart Exam API Test with new JSON...")
    logger.info("=== TESTING SMART EXAM API ===")
    logger.info(f"Endpoint: {url}")
    logger.info(f"Request data: {json.dumps(test_data, indent=2, ensure_ascii=False)}")
    
    try:
        # Make the request
        response = requests.post(
            url,
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=300  # 5 minutes timeout
        )
        
        logger.info(f"Response status: {response.status_code}")
        logger.info(f"Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            logger.info("=== SUCCESS ===")
            logger.info(f"Response: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            # Check if we got the expected fields
            if result.get("success"):
                logger.info("✅ API call successful")
                logger.info(f"✅ Exam ID: {result.get('exam_id')}")
                logger.info(f"✅ Message: {result.get('message')}")
                
                if result.get("online_links"):
                    logger.info("✅ Online links generated:")
                    for link_type, link_url in result["online_links"].items():
                        logger.info(f"   - {link_type}: {link_url}")
                
                if result.get("statistics"):
                    logger.info("✅ Statistics:")
                    stats = result["statistics"]
                    logger.info(f"   - Total questions: {stats.get('total_questions')}")
                    logger.info(f"   - Part 1: {stats.get('part_1_questions')}")
                    logger.info(f"   - Part 2: {stats.get('part_2_questions')}")
                    logger.info(f"   - Part 3: {stats.get('part_3_questions')}")
                    logger.info(f"   - Generation time: {stats.get('generation_time')}s")
            else:
                logger.error("❌ API returned success=False")
                logger.error(f"Error: {result.get('error')}")
        else:
            logger.error("=== ERROR ===")
            logger.error(f"HTTP {response.status_code}: {response.text}")
            
    except requests.exceptions.RequestException as e:
        logger.error(f"❌ Request failed: {e}")
    except json.JSONDecodeError as e:
        logger.error(f"❌ JSON decode error: {e}")
        logger.error(f"Response text: {response.text}")
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    test_smart_exam_api()
