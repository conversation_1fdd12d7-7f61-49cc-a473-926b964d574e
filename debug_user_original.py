#!/usr/bin/env python3

import requests
import json
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_user_original_json():
    """Test với JSON gốc của user"""
    
    url = "http://127.0.0.1:8000/api/v1/exam/generate-smart-exam"
    
    # JSON gốc của user
    data = {
        "school": "Test School",
        "grade": 12,
        "subject": "Sinh học",
        "examTitle": "Test Exam",
        "duration": 45,
        "outputFormat": "docx",
        "outputLink": "online",
        "matrix": [
            {
                "lessonId": "234",
                "totalQuestions": 3,
                "parts": [
                    {
                        "part": 1,
                        "objectives": {
                            "Biết": 2,
                            "Hiểu": 1,
                            "Vận_dụng": 0
                        }
                    }
                ]
            },
            {
                "lessonId": "test1",  # Non-existent lesson ID
                "totalQuestions": 2,
                "parts": [
                    {
                        "part": 1,
                        "objectives": {
                            "Biết": 1,
                            "Hiểu": 1,
                            "Vận_dụng": 0
                        }
                    }
                ]
            }
        ]
    }
    
    logger.info("=== USER ORIGINAL JSON TEST ===")
    logger.info(f"URL: {url}")
    logger.info(f"Data: {json.dumps(data, indent=2, ensure_ascii=False)}")
    
    try:
        response = requests.post(url, json=data, timeout=120)
        logger.info(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            logger.info("=== RESPONSE ===")
            logger.info(f"Success: {result.get('success', False)}")
            logger.info(f"Total questions: {result.get('total_questions', 0)}")
            logger.info(f"Error: {result.get('error', '')}")
            
            if result.get('total_questions', 0) > 0:
                logger.info("✅ SUCCESS: Questions generated!")
                if 'document_link' in result:
                    logger.info(f"Document link: {result['document_link']}")
            else:
                logger.error("❌ FAILED: No questions generated")
                
        else:
            logger.error(f"❌ HTTP Error: {response.status_code}")
            logger.error(f"Response: {response.text}")
            
    except Exception as e:
        logger.error(f"❌ Exception: {e}")

if __name__ == "__main__":
    test_user_original_json()
