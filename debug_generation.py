#!/usr/bin/env python3
"""
Direct debug script to test smart exam generation service
"""
import asyncio
import logging
import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.abspath('.'))

from app.services.smart_exam_generation_service import smart_exam_generation_service
from app.services.exam_content_service import exam_content_service
from app.models.smart_exam_models import SmartExamRequest, LessonMatrixModel, PartModel, ObjectivesModel

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_smart_exam_generation():
    """Test smart exam generation directly"""
    logger.info("=== DIRECT SMART EXAM GENERATION TEST ===")
    
    try:
        # 1. Get lesson content first
        lesson_ids = ["234"]
        logger.info(f"Getting lesson content for: {lesson_ids}")
        
        lesson_content_result = await exam_content_service.get_multiple_lessons_content_for_exam(
            lesson_ids=lesson_ids
        )
        
        if not lesson_content_result.get('success'):
            logger.error(f"Failed to get lesson content: {lesson_content_result.get('error')}")
            return
            
        content_data = lesson_content_result.get('content', {})
        logger.info(f"Got content for lessons: {list(content_data.keys())}")
        
        # 2. Create exam request
        objectives = ObjectivesModel(
            Biết=2,
            Hiểu=1,
            Vận_dụng=0
        )

        part_matrix = PartModel(
            part=1,
            objectives=objectives
        )

        lesson_matrix = LessonMatrixModel(
            lessonId="234",
            totalQuestions=3,
            parts=[part_matrix]
        )
        
        exam_request = SmartExamRequest(
            school="Test School",
            grade=12,
            subject="Sinh học",
            examTitle="Test Exam",
            duration=45,
            outputFormat="docx",
            outputLink="online",
            matrix=[lesson_matrix]
        )
        
        logger.info("Created exam request")
        
        # 3. Test smart exam generation
        logger.info("Testing smart exam generation...")
        
        result = await smart_exam_generation_service.generate_smart_exam(
            exam_request=exam_request,
            lesson_content=content_data
        )
        
        logger.info(f"Generation result success: {result.get('success')}")
        logger.info(f"Total questions generated: {result.get('total_generated', 0)}")
        logger.info(f"Questions: {len(result.get('questions', []))}")
        
        if result.get('error'):
            logger.error(f"Generation error: {result.get('error')}")
            
        if result.get('questions'):
            logger.info("✅ SUCCESS: Questions generated!")
            for i, q in enumerate(result.get('questions', [])[:2]):  # Show first 2 questions
                logger.info(f"Question {i+1}: {q.get('question', 'No question text')[:100]}...")
        else:
            logger.error("❌ FAILED: No questions generated")
            
    except Exception as e:
        logger.error(f"Exception during test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_smart_exam_generation())
