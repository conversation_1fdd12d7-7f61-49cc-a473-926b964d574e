#!/usr/bin/env python3
"""
Test script cho smart exam generation endpoint
"""

import asyncio
import json
import aiohttp

async def test_smart_exam_endpoint():
    """Test endpoint generate-smart-exam"""
    
    url = "http://localhost:8000/api/v1/exam/generate-smart-exam"
    
    payload = {
        "school": "Trường THPT Hong Thinh",
        "grade": 12,
        "subject": "Hoa hoc",
        "examTitle": "Kiểm tra con cat",
        "duration": 90,
        "outputFormat": "docx",
        "outputLink": "online",
        "matrix": [
            {
                "lessonId": "234",
                "totalQuestions": 10,
                "parts": [
                    {
                        "part": 1,
                        "objectives": {
                            "Biết": 3,
                            "Hiểu": 1,
                            "Vận_dụng": 0
                        }
                    },
                    {
                        "part": 2,
                        "objectives": {
                            "Biết": 1,
                            "Hiểu": 3,
                            "Vận_dụng": 0
                        }
                    },
                    {
                        "part": 3,
                        "objectives": {
                            "Biết": 0,
                            "<PERSON>ể<PERSON>": 1,
                            "Vận_dụng": 1
                        }
                    }
                ]
            },
            {
                "lessonId": "test1",
                "totalQuestions": 8,
                "parts": [
                    {
                        "part": 1,
                        "objectives": {
                            "Biết": 2,
                            "Hiểu": 1,
                            "Vận_dụng": 0
                        }
                    },
                    {
                        "part": 2,
                        "objectives": {
                            "Biết": 0,
                            "Hiểu": 3,
                            "Vận_dụng": 0
                        }
                    },
                    {
                        "part": 3,
                        "objectives": {
                            "Biết": 0,
                            "Hiểu": 1,
                            "Vận_dụng": 1
                        }
                    }
                ]
            }
        ]
    }
    
    try:
        print("Testing smart exam generation endpoint...")
        print(f"URL: {url}")
        print("Payload structure:")
        print(f"  School: {payload['school']}")
        print(f"  Subject: {payload['subject']}")
        print(f"  Matrix lessons: {[m['lessonId'] for m in payload['matrix']]}")
        
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=payload) as response:
                print(f"\nResponse status: {response.status}")
                
                response_text = await response.text()
                print(f"Response length: {len(response_text)}")
                
                try:
                    response_json = json.loads(response_text)
                    print(f"Response JSON keys: {list(response_json.keys())}")
                    
                    if response_json.get("success"):
                        print("SUCCESS: Exam generated successfully!")
                        print(f"Total questions: {response_json.get('total_generated', 0)}")
                        
                        # In ra một vài câu hỏi mẫu
                        questions = response_json.get("questions", [])
                        if questions:
                            print(f"\nSample questions (first 2):")
                            for i, q in enumerate(questions[:2]):
                                print(f"Question {i+1}:")
                                print(f"  Part: {q.get('part', 'N/A')}")
                                print(f"  Level: {q.get('cognitive_level', 'N/A')}")
                                print(f"  Question: {q.get('question', 'N/A')[:100]}...")
                                print(f"  Answer: {q.get('answer', 'N/A')}")
                                print()
                    else:
                        print("FAILED: Exam generation failed")
                        print(f"Error: {response_json.get('error', 'Unknown error')}")
                        
                except json.JSONDecodeError:
                    print("Response is not valid JSON:")
                    print(response_text[:1000])
                    
    except Exception as e:
        print(f"Error testing endpoint: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_smart_exam_endpoint())
