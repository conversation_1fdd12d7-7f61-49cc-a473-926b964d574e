import requests
import json

# Load test data
with open('test_request.json', 'r', encoding='utf-8') as f:
    test_data = json.load(f)

# Make API request
url = "http://127.0.0.1:8000/api/v1/exam/generate-smart-exam"
headers = {"Content-Type": "application/json"}

print("Sending request to:", url)
print("Request data:", json.dumps(test_data, indent=2, ensure_ascii=False))

try:
    response = requests.post(url, json=test_data, headers=headers, timeout=120)
    print(f"\nStatus Code: {response.status_code}")
    print(f"Response: {response.text}")
    
    if response.status_code == 200:
        result = response.json()
        print("\n=== SUCCESS ===")
        print(json.dumps(result, indent=2, ensure_ascii=False))
    else:
        print(f"\n=== ERROR ===")
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
        
except requests.exceptions.RequestException as e:
    print(f"Request failed: {e}")
