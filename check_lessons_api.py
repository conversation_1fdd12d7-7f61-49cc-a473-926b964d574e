"""
Kiểm tra lesson ID có sẵn qua API
"""

import requests
import json

def check_available_lessons():
    """Kiểm tra lesson ID có sẵn"""
    
    # Kiểm tra textbook list
    print("=== CHECKING AVAILABLE TEXTBOOKS ===")
    try:
        url = "http://localhost:8000/api/v1/pdf/getAllTextBook"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            textbooks = data.get("textbooks", [])
            print(f"Found {len(textbooks)} textbooks:")
            
            for book in textbooks[:3]:  # Show first 3 books
                book_id = book.get("book_id", "N/A")
                title = book.get("title", "N/A")
                print(f"  - Book ID: {book_id}")
                print(f"    Title: {title}")
                
                # Check chapters and lessons
                chapters = book.get("chapters", [])
                print(f"    Chapters: {len(chapters)}")
                
                for chapter in chapters[:2]:  # First 2 chapters
                    chapter_title = chapter.get("title", "N/A")
                    lessons = chapter.get("lessons", [])
                    print(f"      Chapter: {chapter_title} ({len(lessons)} lessons)")
                    
                    for lesson in lessons[:3]:  # First 3 lessons
                        lesson_id = lesson.get("lesson_id", "N/A")
                        lesson_title = lesson.get("title", "N/A")
                        print(f"        - Lesson ID: {lesson_id}")
                        print(f"          Title: {lesson_title}")
                
                print()
        else:
            print(f"Failed to get textbooks: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"Error checking textbooks: {e}")

def test_with_real_lesson_id(lesson_id):
    """Test với lesson ID thật"""
    print(f"\n=== TESTING WITH LESSON ID: {lesson_id} ===")
    
    test_request = {
        "school": "THPT ABC",
        "grade": 12,
        "subject": "Hóa học",
        "examTitle": "Kiểm tra định dạng hóa học",
        "duration": 50,
        "outputFormat": "docx",
        "outputLink": "online",
        "matrix": [
            {
                "lessonId": lesson_id,
                "totalQuestions": 3,
                "parts": [
                    {
                        "part": 1,
                        "objectives": {
                            "Biết": 1,
                            "Hiểu": 0,
                            "Vận_dụng": 0
                        }
                    },
                    {
                        "part": 2,
                        "objectives": {
                            "Biết": 1,
                            "Hiểu": 0,
                            "Vận_dụng": 0
                        }
                    },
                    {
                        "part": 3,
                        "objectives": {
                            "Biết": 0,
                            "Hiểu": 1,
                            "Vận_dụng": 0
                        }
                    }
                ]
            }
        ]
    }
    
    try:
        url = "http://localhost:8000/api/v1/exam/generate-smart-exam"
        response = requests.post(url, json=test_request, timeout=120)
        
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            success = result.get("success", False)
            print(f"Success: {success}")
            
            if success:
                print("EXAM GENERATED SUCCESSFULLY!")
                if result.get("online_links"):
                    print(f"Online links: {result['online_links']}")
            else:
                print(f"Error: {result.get('error', 'Unknown error')}")
                print(f"Message: {result.get('message', 'No message')}")
        else:
            print(f"HTTP Error: {response.text}")
            
    except Exception as e:
        print(f"Error testing lesson ID: {e}")

if __name__ == "__main__":
    print("Checking Available Lessons")
    print("=" * 50)
    
    # First check what lessons are available
    check_available_lessons()
    
    # Then test with a real lesson ID (you'll need to update this)
    # test_with_real_lesson_id("REAL_LESSON_ID_HERE")
