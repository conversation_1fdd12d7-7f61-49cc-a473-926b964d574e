#!/usr/bin/env python3
"""
Simple debug script to test smart exam generation
"""
import requests
import json
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_simple_exam():
    """Test with minimal data"""
    url = "http://127.0.0.1:8000/api/v1/exam/generate-smart-exam"
    
    # Simple test data with only one lesson and minimal questions
    data = {
        "school": "Test School",
        "grade": 12,
        "subject": "Sinh học",
        "examTitle": "Test Exam",
        "duration": 45,
        "outputFormat": "docx",
        "outputLink": "online",
        "matrix": [
            {
                "lessonId": "234",  # Known existing lesson
                "totalQuestions": 3,
                "parts": [
                    {
                        "part": 1,
                        "objectives": {
                            "Biết": 2,
                            "Hiểu": 1,
                            "Vận_dụng": 0
                        }
                    }
                ]
            }
        ]
    }
    
    logger.info("=== SIMPLE DEBUG TEST ===")
    logger.info(f"URL: {url}")
    logger.info(f"Data: {json.dumps(data, indent=2, ensure_ascii=False)}")
    
    try:
        response = requests.post(url, json=data, timeout=60)
        logger.info(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            logger.info("=== RESPONSE ===")
            logger.info(f"Success: {result.get('success')}")
            logger.info(f"Total questions: {result.get('statistics', {}).get('total_questions', 0)}")
            logger.info(f"Error: {result.get('error', 'None')}")
            
            if result.get('statistics', {}).get('total_questions', 0) > 0:
                logger.info("✅ SUCCESS: Questions generated!")
            else:
                logger.error("❌ FAILED: No questions generated")
                
        else:
            logger.error(f"HTTP Error: {response.status_code}")
            logger.error(f"Response: {response.text}")
            
    except Exception as e:
        logger.error(f"Request failed: {e}")

if __name__ == "__main__":
    test_simple_exam()
