#!/usr/bin/env python3
"""
Test script cho smart exam generation - direct call
"""

import asyncio
import sys
import os

# Add the app directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.models.smart_exam_models import SmartExamRequest, LessonMatrixModel, PartModel, ObjectivesModel
from app.services.smart_exam_generation_service import smart_exam_generation_service
from app.services.exam_content_service import exam_content_service

async def test_smart_exam_direct():
    """Test smart exam generation directly"""
    
    try:
        print("Testing smart exam generation directly...")
        
        # Tạo request object
        request = SmartExamRequest(
            school="Truong THPT Hong Thinh",
            grade=12,
            subject="Hoa hoc",
            examTitle="Kiem tra con cat",
            duration=90,
            outputFormat="docx",
            outputLink="online",
            matrix=[
                LessonMatrixModel(
                    lessonId="234",
                    totalQuestions=10,
                    parts=[
                        PartModel(
                            part=1,
                            objectives=ObjectivesModel(Biết=3, Hiểu=1, Vận_dụng=0)
                        ),
                        PartModel(
                            part=2,
                            objectives=ObjectivesModel(Biết=1, Hiểu=3, Vận_dụng=0)
                        ),
                        PartModel(
                            part=3,
                            objectives=ObjectivesModel(Biết=0, Hiểu=1, Vận_dụng=1)
                        )
                    ]
                ),
                LessonMatrixModel(
                    lessonId="test1",
                    totalQuestions=8,
                    parts=[
                        PartModel(
                            part=1,
                            objectives=ObjectivesModel(Biết=2, Hiểu=1, Vận_dụng=0)
                        ),
                        PartModel(
                            part=2,
                            objectives=ObjectivesModel(Biết=0, Hiểu=3, Vận_dụng=0)
                        ),
                        PartModel(
                            part=3,
                            objectives=ObjectivesModel(Biết=0, Hiểu=1, Vận_dụng=1)
                        )
                    ]
                )
            ]
        )
        
        print(f"Request created with {len(request.matrix)} lessons")
        
        # Lấy lesson content
        lesson_ids = [lesson.lessonId for lesson in request.matrix]
        print(f"Getting content for lessons: {lesson_ids}")
        
        lesson_content = await exam_content_service.get_multiple_lessons_content_for_exam(
            lesson_ids=lesson_ids
        )
        
        print(f"Content retrieval success: {lesson_content.get('success', False)}")
        print(f"Successful lessons: {lesson_content.get('successful_lessons', [])}")
        print(f"Failed lessons: {lesson_content.get('failed_lessons', [])}")
        
        if not lesson_content.get("success", False):
            print(f"ERROR: Failed to get lesson content: {lesson_content.get('error')}")
            return
        
        content_data = lesson_content.get("content", {})
        print(f"Content data keys: {list(content_data.keys())}")
        
        # Debug content structure
        for lesson_id, lesson_data in content_data.items():
            print(f"Lesson {lesson_id}:")
            print(f"  Type: {type(lesson_data)}")
            if isinstance(lesson_data, dict):
                print(f"  Keys: {list(lesson_data.keys())}")
                if "content" in lesson_data:
                    content = lesson_data["content"]
                    print(f"  Content type: {type(content)}")
                    if isinstance(content, dict):
                        print(f"  Content keys: {list(content.keys())}")
                        main_content = content.get("main_content", "")
                        print(f"  Main content length: {len(str(main_content))}")
                        print(f"  Main content preview: {str(main_content)[:100]}...")
        
        # Tạo đề thi
        print("\nGenerating smart exam...")
        exam_result = await smart_exam_generation_service.generate_smart_exam(
            exam_request=request, lesson_content=content_data
        )
        
        print(f"Exam generation success: {exam_result.get('success', False)}")
        
        if exam_result.get("success", False):
            print(f"Total questions generated: {exam_result.get('total_generated', 0)}")
            
            questions = exam_result.get("questions", [])
            if questions:
                print(f"\nFirst 2 questions:")
                for i, q in enumerate(questions[:2]):
                    print(f"Question {i+1}:")
                    print(f"  Part: {q.get('part', 'N/A')}")
                    print(f"  Level: {q.get('cognitive_level', 'N/A')}")
                    print(f"  Question: {str(q.get('question', 'N/A'))[:100]}...")
                    print(f"  Answer: {q.get('answer', 'N/A')}")
                    print()
        else:
            print(f"ERROR: Exam generation failed: {exam_result.get('error')}")
                
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_smart_exam_direct())
