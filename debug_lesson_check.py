#!/usr/bin/env python3

import asyncio
import sys
import os

# Add the project root to Python path
# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.exam_content_service import ExamContentService
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def check_lesson_content():
    """Kiểm tra nội dung lesson có tồn tại không"""
    
    try:
        # Initialize service
        content_service = ExamContentService()
        
        # Test lesson IDs
        lesson_ids = ["234", "test1"]
        
        logger.info("=== CHECKING LESSON CONTENT ===")
        
        for lesson_id in lesson_ids:
            logger.info(f"\nChecking lesson ID: {lesson_id}")
            
            # Get content for this lesson
            content_data = await content_service.get_lesson_content_for_exam([lesson_id])
            
            logger.info(f"Content data keys: {list(content_data.keys())}")
            
            if lesson_id in content_data:
                lesson_data = content_data[lesson_id]
                logger.info(f"Lesson {lesson_id} found!")
                logger.info(f"Data type: {type(lesson_data)}")
                
                if isinstance(lesson_data, dict):
                    logger.info(f"Data keys: {list(lesson_data.keys())}")
                    
                    # Check nested structure
                    if "content" in lesson_data:
                        content = lesson_data["content"]
                        logger.info(f"Content type: {type(content)}")
                        if isinstance(content, dict):
                            logger.info(f"Content keys: {list(content.keys())}")
                            
                            if "main_content" in content:
                                main_content = content["main_content"]
                                logger.info(f"Main content length: {len(str(main_content))}")
                                logger.info(f"Main content preview: {str(main_content)[:200]}...")
                            else:
                                logger.warning("No 'main_content' in content")
                        else:
                            logger.info(f"Content value: {content}")
                    else:
                        logger.warning("No 'content' key in lesson data")
            else:
                logger.warning(f"Lesson {lesson_id} NOT FOUND!")
                
    except Exception as e:
        logger.error(f"Error checking lesson content: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(check_lesson_content())
